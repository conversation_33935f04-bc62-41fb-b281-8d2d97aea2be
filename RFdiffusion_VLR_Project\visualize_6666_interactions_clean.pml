# PyMOL script for protein chain interaction visualization
# Usage: @visualize_6666_interactions_clean.pml

# Load PDB file
load output_6666_interactions.pdb
set_name output_6666_interactions, protein_complex

# Basic display settings - only cartoon for all residues
hide everything
show cartoon
color gray80, all
set cartoon_transparency, 0.3

# Color chains
color lightblue, chain A
color lightpink, chain B

# Define interaction residues
select interaction_residues, (chain A and resi 23+24+26+42+44+45+64+66+68+88+92+93+112+114+116+117+138+140+161+163+185+215+216+217+218) or (chain B and resi 10+74+76+77+78+79+80+152+199+200+201+229+230+231+232+233+234+235+236+237+238)

# Show sticks and spheres ONLY for interaction residues
show sticks, interaction_residues
show spheres, interaction_residues and name CA
set sphere_scale, 0.3, interaction_residues and name CA

# Color interaction residues
color red, chain A and interaction_residues
color blue, chain B and interaction_residues

# Mark important sites (>20 interactions) with yellow spheres
select important_sites, (chain A and resi 44+45+93+215) or (chain B and resi 77+78+232+237)
color yellow, important_sites and name CA
set sphere_scale, 0.5, important_sites and name CA

# Add labels for important sites
label chain A and resi 44 and name CA, 'A44TYR'
label chain A and resi 45 and name CA, 'A45ASN'
label chain A and resi 93 and name CA, 'A93GLU'
label chain A and resi 215 and name CA, 'A215TRP'
label chain B and resi 77 and name CA, 'B77ASP'
label chain B and resi 78 and name CA, 'B78HIS'
label chain B and resi 232 and name CA, 'B232HIS'
label chain B and resi 237 and name CA, 'B237LEU'

# Show distances for closest interactions
distance dist_1, chain A and resi 45 and name CA, chain B and resi 77 and name CA
distance dist_2, chain A and resi 44 and name CA, chain B and resi 77 and name CA
distance dist_3, chain A and resi 24 and name CA, chain B and resi 77 and name CA
distance dist_4, chain A and resi 44 and name CA, chain B and resi 78 and name CA
distance dist_5, chain A and resi 93 and name CA, chain B and resi 78 and name CA

# View and display settings
orient
zoom
set label_size, 20
set label_color, black
set distance_color, yellow
set dash_color, yellow
set dash_width, 2

# Create different views
# View 1: Overall structure
scene view1, store, Overall_structure

# View 2: Only interaction sites
hide everything
show cartoon, all
set cartoon_transparency, 0.7
show sticks, interaction_residues
show spheres, interaction_residues and name CA
scene view2, store, Interaction_sites_only

# View 3: Surface view
hide everything
show surface
set surface_color, white
color red, chain A and interaction_residues
color blue, chain B and interaction_residues
set transparency, 0.5
scene view3, store, Surface_view

# Return to overall structure view
scene view1, recall

# Script complete
print 'Interaction visualization script loaded successfully!'
print 'Use: scene view1/view2/view3 to switch between different views'
print 'Only residues with inter-chain interactions are shown as sticks/spheres'
