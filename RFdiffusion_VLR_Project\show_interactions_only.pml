# PyMOL script: Show only inter-chain interaction residues as sticks
# All other residues shown as cartoon with 30% transparency

# Load structure
load output_6666_interactions.pdb

# Basic setup: hide everything first
hide everything

# Show cartoon for all residues with 30% transparency
show cartoon, all
set cartoon_transparency, 0.3

# Color chains
color lightblue, chain A
color lightpink, chain B

# Define ONLY the residues that have inter-chain interactions
select interacting_residues, (chain A and resi 23+24+26+42+44+45+64+66+68+88+92+93+112+114+116+117+138+140+161+163+185+215+216+217+218) or (chain B and resi 10+74+76+77+78+79+80+152+199+200+201+229+230+231+232+233+234+235+236+237+238)

# Show sticks ONLY for interacting residues
show sticks, interacting_residues

# Color the interacting residues differently
color red, chain A and interacting_residues
color blue, chain B and interacting_residues

# Optional: show CA atoms as small spheres for interacting residues
show spheres, interacting_residues and name CA
set sphere_scale, 0.2, interacting_residues and name CA

# Highlight the most important interaction sites (>40 interactions)
select hotspots, (chain A and resi 44+215) or (chain B and resi 77+232)
color yellow, hotspots
set sphere_scale, 0.4, hotspots and name CA

# Final view settings
orient
zoom
set stick_radius, 0.15

print "Visualization complete!"
print "Only inter-chain interacting residues are shown as sticks"
print "All other residues are shown as cartoon with 30% transparency"
