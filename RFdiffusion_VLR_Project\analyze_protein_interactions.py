#!/usr/bin/env python3
"""
蛋白质链间相互作用分析脚本
分析PDB文件中两条链的相互作用位点，并标记出相互作用的残基
"""

import numpy as np
import sys
from collections import defaultdict
import argparse

class PDBAtom:
    """PDB原子类"""
    def __init__(self, line):
        self.line = line
        self.atom_id = int(line[6:11].strip())
        self.atom_name = line[12:16].strip()
        self.residue_name = line[17:20].strip()
        self.chain_id = line[21].strip()
        self.residue_id = int(line[22:26].strip())
        self.x = float(line[30:38].strip())
        self.y = float(line[38:46].strip())
        self.z = float(line[46:54].strip())
        self.occupancy = float(line[54:60].strip()) if line[54:60].strip() else 1.0
        self.b_factor = float(line[60:66].strip()) if line[60:66].strip() else 0.0
        self.element = line[76:78].strip() if len(line) > 76 else ""
        
    def get_coords(self):
        """获取原子坐标"""
        return np.array([self.x, self.y, self.z])
    
    def distance_to(self, other_atom):
        """计算到另一个原子的距离"""
        return np.linalg.norm(self.get_coords() - other_atom.get_coords())
    
    def is_backbone(self):
        """判断是否为主链原子"""
        return self.atom_name in ['N', 'CA', 'C', 'O']
    
    def is_sidechain(self):
        """判断是否为侧链原子"""
        return not self.is_backbone() and self.atom_name != 'CB'

class PDBAnalyzer:
    """PDB文件分析器"""
    
    def __init__(self, pdb_file):
        self.pdb_file = pdb_file
        self.atoms = []
        self.chain_atoms = defaultdict(list)
        self.residues = defaultdict(list)
        self.interaction_residues = set()
        
    def parse_pdb(self):
        """解析PDB文件"""
        print(f"正在解析PDB文件: {self.pdb_file}")
        
        with open(self.pdb_file, 'r') as f:
            for line in f:
                if line.startswith('ATOM'):
                    atom = PDBAtom(line)
                    self.atoms.append(atom)
                    self.chain_atoms[atom.chain_id].append(atom)
                    self.residues[(atom.chain_id, atom.residue_id)].append(atom)
        
        print(f"解析完成，共找到 {len(self.atoms)} 个原子")
        print(f"链信息: {dict([(k, len(v)) for k, v in self.chain_atoms.items()])}")
    
    def find_interactions(self, distance_cutoff=4.5):
        """寻找链间相互作用"""
        print(f"正在寻找链间相互作用 (距离阈值: {distance_cutoff} Å)")
        
        chains = list(self.chain_atoms.keys())
        if len(chains) < 2:
            print("错误：PDB文件中少于2条链")
            return
        
        chain_a, chain_b = chains[0], chains[1]
        print(f"分析链 {chain_a} 和链 {chain_b} 之间的相互作用")
        
        interactions = []
        
        # 计算所有原子对之间的距离
        for atom_a in self.chain_atoms[chain_a]:
            for atom_b in self.chain_atoms[chain_b]:
                distance = atom_a.distance_to(atom_b)
                
                if distance <= distance_cutoff:
                    interactions.append({
                        'atom_a': atom_a,
                        'atom_b': atom_b,
                        'distance': distance,
                        'residue_a': (chain_a, atom_a.residue_id),
                        'residue_b': (chain_b, atom_b.residue_id),
                        'interaction_type': self.classify_interaction(atom_a, atom_b)
                    })
                    
                    # 记录相互作用的残基
                    self.interaction_residues.add((chain_a, atom_a.residue_id))
                    self.interaction_residues.add((chain_b, atom_b.residue_id))
        
        print(f"找到 {len(interactions)} 个原子间相互作用")
        print(f"涉及 {len(self.interaction_residues)} 个残基")
        
        return interactions
    
    def classify_interaction(self, atom_a, atom_b):
        """分类相互作用类型"""
        if atom_a.is_backbone() and atom_b.is_backbone():
            return "backbone-backbone"
        elif atom_a.is_sidechain() and atom_b.is_sidechain():
            return "sidechain-sidechain"
        elif (atom_a.is_backbone() and atom_b.is_sidechain()) or \
             (atom_a.is_sidechain() and atom_b.is_backbone()):
            return "backbone-sidechain"
        else:
            return "other"
    
    def generate_modified_pdb(self, output_file, interactions):
        """生成修改后的PDB文件，标记相互作用位点"""
        print(f"正在生成修改后的PDB文件: {output_file}")
        
        # 创建残基到相互作用信息的映射
        residue_interactions = defaultdict(list)
        for interaction in interactions:
            residue_interactions[interaction['residue_a']].append(interaction)
            residue_interactions[interaction['residue_b']].append(interaction)
        
        with open(self.pdb_file, 'r') as infile, open(output_file, 'w') as outfile:
            # 写入头部注释
            outfile.write("REMARK   1 PROTEIN CHAIN INTERACTION ANALYSIS\n")
            outfile.write("REMARK   2 INTERACTION SITES MARKED WITH HIGH B-FACTORS\n")
            outfile.write("REMARK   3 B-FACTOR CODING:\n")
            outfile.write("REMARK   4   99.99 = INTERACTION SITE\n")
            outfile.write("REMARK   5   50.00 = NEAR INTERACTION SITE\n")
            outfile.write("REMARK   6   ORIGINAL = NON-INTERACTION SITE\n")
            
            for line in infile:
                if line.startswith('ATOM'):
                    atom = PDBAtom(line)
                    residue_key = (atom.chain_id, atom.residue_id)
                    
                    # 修改B因子来标记相互作用位点
                    if residue_key in self.interaction_residues:
                        # 相互作用位点设置为99.99
                        new_b_factor = 99.99
                    else:
                        # 保持原始B因子
                        new_b_factor = atom.b_factor
                    
                    # 重新构建ATOM行，修改B因子
                    new_line = (
                        f"{line[:60]}"  # 前60个字符保持不变
                        f"{new_b_factor:6.2f}"  # B因子
                        f"{line[66:]}"  # 其余部分保持不变
                    )
                    outfile.write(new_line)
                else:
                    outfile.write(line)
        
        print(f"修改后的PDB文件已保存到: {output_file}")
    
    def generate_interaction_report(self, interactions, report_file):
        """生成相互作用报告"""
        print(f"正在生成相互作用报告: {report_file}")
        
        # 按残基分组统计
        residue_stats = defaultdict(lambda: {
            'backbone_interactions': 0,
            'sidechain_interactions': 0,
            'total_interactions': 0,
            'min_distance': float('inf'),
            'partner_residues': set()
        })
        
        for interaction in interactions:
            res_a = interaction['residue_a']
            res_b = interaction['residue_b']
            
            # 更新统计信息
            for res in [res_a, res_b]:
                partner = res_b if res == res_a else res_a
                residue_stats[res]['partner_residues'].add(partner)
                residue_stats[res]['total_interactions'] += 1
                residue_stats[res]['min_distance'] = min(
                    residue_stats[res]['min_distance'], 
                    interaction['distance']
                )
                
                if interaction['interaction_type'] == 'backbone-backbone':
                    residue_stats[res]['backbone_interactions'] += 1
                elif interaction['interaction_type'] == 'sidechain-sidechain':
                    residue_stats[res]['sidechain_interactions'] += 1
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Protein Chain Interaction Analysis Report\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"Total interactions: {len(interactions)}\n")
            f.write(f"Interacting residues: {len(self.interaction_residues)}\n\n")

            f.write("Detailed interaction information:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'Chain':<5} {'ResID':<6} {'ResName':<7} {'Total':<6} {'Backbone':<8} {'Sidechain':<9} {'MinDist':<8} {'Partners'}\n")
            f.write("-" * 80 + "\n")
            
            for (chain_id, res_id) in sorted(self.interaction_residues):
                res_key = (chain_id, res_id)
                stats = residue_stats[res_key]
                
                # 获取残基名称
                res_atoms = self.residues[res_key]
                res_name = res_atoms[0].residue_name if res_atoms else "UNK"
                
                partners = ", ".join([f"{c}{r}" for c, r in sorted(stats['partner_residues'])])
                
                f.write(f"{chain_id:<5} {res_id:<6} {res_name:<7} {stats['total_interactions']:<6} "
                       f"{stats['backbone_interactions']:<8} {stats['sidechain_interactions']:<9} "
                       f"{stats['min_distance']:<8.2f} {partners}\n")
        
        print(f"相互作用报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='分析蛋白质链间相互作用')
    parser.add_argument('input_pdb', help='输入PDB文件路径')
    parser.add_argument('-o', '--output', default='modified.pdb', help='输出PDB文件路径')
    parser.add_argument('-r', '--report', default='interaction_report.txt', help='相互作用报告文件路径')
    parser.add_argument('-d', '--distance', type=float, default=4.5, help='相互作用距离阈值 (Å)')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = PDBAnalyzer(args.input_pdb)
    
    # 解析PDB文件
    analyzer.parse_pdb()
    
    # 寻找相互作用
    interactions = analyzer.find_interactions(args.distance)
    
    if interactions:
        # 生成修改后的PDB文件
        analyzer.generate_modified_pdb(args.output, interactions)
        
        # 生成相互作用报告
        analyzer.generate_interaction_report(interactions, args.report)
        
        print("\n分析完成！")
        print(f"修改后的PDB文件: {args.output}")
        print(f"相互作用报告: {args.report}")
        print("\n在PyMOL中可以使用以下命令来可视化相互作用位点:")
        print(f"load {args.output}")
        print("spectrum b, rainbow, minimum=0, maximum=100")
        print("show sticks, b > 90")
    else:
        print("未找到相互作用位点")

if __name__ == "__main__":
    main()
