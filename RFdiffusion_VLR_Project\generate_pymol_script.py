#!/usr/bin/env python3
"""
生成PyMOL可视化脚本，用于展示蛋白质链间相互作用
"""

import argparse
from collections import defaultdict

def parse_interaction_report(report_file):
    """解析相互作用报告文件"""
    interactions = []
    
    with open(report_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        
        # 跳过头部，找到数据行
        data_start = False
        for line in lines:
            if 'Chain' in line and 'ResID' in line:
                data_start = True
                continue
            
            if data_start and line.strip() and not line.startswith('-'):
                parts = line.strip().split()
                if len(parts) >= 8:
                    chain = parts[0]
                    res_id = parts[1]
                    res_name = parts[2]
                    total_interactions = int(parts[3])
                    backbone_interactions = int(parts[4])
                    sidechain_interactions = int(parts[5])
                    min_distance = float(parts[6])
                    partners = ' '.join(parts[7:]) if len(parts) > 7 else ""
                    
                    interactions.append({
                        'chain': chain,
                        'res_id': res_id,
                        'res_name': res_name,
                        'total_interactions': total_interactions,
                        'backbone_interactions': backbone_interactions,
                        'sidechain_interactions': sidechain_interactions,
                        'min_distance': min_distance,
                        'partners': partners
                    })
    
    return interactions

def generate_pymol_script(pdb_file, interactions, output_script):
    """生成PyMOL可视化脚本"""
    
    with open(output_script, 'w') as f:
        f.write("# PyMOL脚本：蛋白质链间相互作用可视化\n")
        f.write("# 使用方法：在PyMOL中运行 @{}\n\n".format(output_script))
        
        # 加载结构
        f.write(f"# 加载PDB文件\n")
        f.write(f"load {pdb_file}\n")
        f.write("set_name {}, protein_complex\n\n".format(pdb_file.split('/')[-1].replace('.pdb', '')))
        
        # 基本设置
        f.write("# 基本显示设置\n")
        f.write("hide everything\n")
        f.write("show cartoon\n")
        f.write("color gray80, all\n")
        f.write("set cartoon_transparency, 0.3\n\n")
        
        # 按链着色
        f.write("# 按链着色\n")
        f.write("color lightblue, chain A\n")
        f.write("color lightpink, chain B\n\n")
        
        # 根据B因子着色相互作用位点
        f.write("# 根据B因子标记相互作用位点\n")
        f.write("spectrum b, rainbow, minimum=0, maximum=100\n")
        f.write("color red, b > 90\n\n")
        
        # 显示相互作用位点
        f.write("# 显示相互作用位点\n")
        f.write("show sticks, b > 90\n")
        f.write("show spheres, b > 90 and name CA\n")
        f.write("set sphere_scale, 0.3, name CA\n\n")
        
        # 创建选择集
        chain_a_residues = []
        chain_b_residues = []
        
        for interaction in interactions:
            if interaction['chain'] == 'A':
                chain_a_residues.append(interaction['res_id'])
            else:
                chain_b_residues.append(interaction['res_id'])
        
        if chain_a_residues:
            f.write("# 创建链A相互作用位点选择集\n")
            f.write("select chain_A_interactions, chain A and resi {}\n".format('+'.join(chain_a_residues)))
            f.write("color red, chain_A_interactions\n")
            f.write("show sticks, chain_A_interactions\n\n")
        
        if chain_b_residues:
            f.write("# 创建链B相互作用位点选择集\n")
            f.write("select chain_B_interactions, chain B and resi {}\n".format('+'.join(chain_b_residues)))
            f.write("color blue, chain_B_interactions\n")
            f.write("show sticks, chain_B_interactions\n\n")
        
        # 标记重要的相互作用位点
        f.write("# 标记重要的相互作用位点（相互作用数>20）\n")
        important_residues = []
        for interaction in interactions:
            if interaction['total_interactions'] > 20:
                residue_sel = f"chain {interaction['chain']} and resi {interaction['res_id']}"
                important_residues.append(residue_sel)
                f.write(f"# {interaction['chain']}{interaction['res_id']} {interaction['res_name']} - {interaction['total_interactions']} 个相互作用\n")
        
        if important_residues:
            f.write("select important_sites, {}\n".format(' or '.join(important_residues)))
            f.write("color yellow, important_sites\n")
            f.write("show spheres, important_sites and name CA\n")
            f.write("set sphere_scale, 0.5, important_sites and name CA\n\n")
        
        # 添加标签
        f.write("# 为重要位点添加标签\n")
        for interaction in interactions:
            if interaction['total_interactions'] > 20:
                f.write(f"label chain {interaction['chain']} and resi {interaction['res_id']} and name CA, "
                       f"'{interaction['chain']}{interaction['res_id']}{interaction['res_name']}'\n")
        f.write("\n")
        
        # 距离测量
        f.write("# 显示最近的相互作用距离\n")
        closest_interactions = sorted(interactions, key=lambda x: x['min_distance'])[:5]
        for i, interaction in enumerate(closest_interactions):
            partners = interaction['partners'].split(', ')
            if partners and partners[0]:
                partner = partners[0]  # 取第一个伙伴
                if len(partner) > 1:
                    partner_chain = partner[0]
                    partner_res = partner[1:]
                    f.write(f"distance dist_{i+1}, chain {interaction['chain']} and resi {interaction['res_id']} and name CA, "
                           f"chain {partner_chain} and resi {partner_res} and name CA\n")
        f.write("\n")
        
        # 视角设置
        f.write("# 视角和显示设置\n")
        f.write("orient\n")
        f.write("zoom\n")
        f.write("set label_size, 20\n")
        f.write("set label_color, black\n")
        f.write("set distance_color, yellow\n")
        f.write("set dash_color, yellow\n")
        f.write("set dash_width, 2\n\n")
        
        # 创建不同视图
        f.write("# 创建不同的视图\n")
        f.write("# 视图1：整体结构\n")
        f.write("scene view1, store, 整体结构\n\n")
        
        f.write("# 视图2：仅显示相互作用位点\n")
        f.write("hide everything\n")
        f.write("show sticks, b > 90\n")
        f.write("show cartoon, all\n")
        f.write("set cartoon_transparency, 0.7\n")
        f.write("scene view2, store, 相互作用位点\n\n")
        
        f.write("# 视图3：表面显示\n")
        f.write("hide everything\n")
        f.write("show surface\n")
        f.write("set surface_color, white\n")
        f.write("color red, b > 90\n")
        f.write("set transparency, 0.5\n")
        f.write("scene view3, store, 表面视图\n\n")
        
        # 恢复到视图1
        f.write("# 恢复到整体结构视图\n")
        f.write("scene view1, recall\n\n")
        
        f.write("# 脚本完成\n")
        f.write("print '相互作用可视化脚本加载完成！'\n")
        f.write("print '使用 scene view1/view2/view3 切换不同视图'\n")

def main():
    parser = argparse.ArgumentParser(description='生成PyMOL可视化脚本')
    parser.add_argument('pdb_file', help='PDB文件路径')
    parser.add_argument('report_file', help='相互作用报告文件路径')
    parser.add_argument('-o', '--output', default='visualize_interactions.pml', help='输出PyMOL脚本文件路径')
    
    args = parser.parse_args()
    
    # 解析相互作用报告
    print(f"正在解析相互作用报告: {args.report_file}")
    interactions = parse_interaction_report(args.report_file)
    print(f"解析完成，找到 {len(interactions)} 个相互作用残基")
    
    # 生成PyMOL脚本
    print(f"正在生成PyMOL脚本: {args.output}")
    generate_pymol_script(args.pdb_file, interactions, args.output)
    print(f"PyMOL脚本已生成: {args.output}")
    
    print("\n使用方法:")
    print("1. 在PyMOL中运行: @{}".format(args.output))
    print("2. 或者在PyMOL命令行中逐行执行脚本内容")
    print("3. 使用 scene view1/view2/view3 切换不同视图")

if __name__ == "__main__":
    main()
