# 蛋白质对接指南

## 已完成的准备工作

✅ **链分离完成**
- `chain_A.pdb` - 受体链A (1943个原子)
- `chain_B.pdb` - 配体链B (1901个原子)
- `docking_analysis.pml` - PyMOL对接分析脚本

## 推荐的对接方法

### 1. 在线对接服务器（推荐）

#### ClusPro (免费，哈佛大学)
- 网址: https://cluspro.bu.edu/
- 上传: `chain_A.pdb` (受体) 和 `chain_B.pdb` (配体)
- 特点: 快速、准确、免费
- 结果: 提供多个对接模型

#### HADDOCK (免费，乌得勒支大学)
- 网址: https://wenmr.science.uu.nl/haddock2.4/
- 上传: 两个PDB文件
- 特点: 可以加入实验约束
- 结果: 高质量对接模型

#### ZDOCK (免费，波士顿大学)
- 网址: https://zdock.umassmed.edu/
- 上传: 受体和配体PDB文件
- 特点: 快速刚体对接
- 结果: 多个对接姿态

### 2. 本地PyMOL对接（简单测试）

运行以下命令在PyMOL中进行简单对接测试：
```
@docking_analysis.pml
```

### 3. 专业软件对接

#### AutoDock Vina
```bash
# 安装AutoDock Vina
# 准备受体和配体文件
# 运行对接
vina --receptor chain_A.pdbqt --ligand chain_B.pdbqt --out docked.pdbqt
```

#### HADDOCK本地版
```bash
# 需要安装HADDOCK
# 配置对接参数
# 运行对接计算
```

## 对接结果分析

### 评价指标
1. **结合能** - 越负越好
2. **RMSD** - 与原始结构的偏差
3. **接触面积** - 蛋白质间接触面积
4. **氢键数量** - 界面氢键数量
5. **形状互补性** - 几何匹配度

### PyMOL分析脚本
```pymol
# 加载对接结果
load docked_result.pdb

# 分析界面
select interface_A, chain A within 5 of chain B
select interface_B, chain B within 5 of chain A

# 显示界面
show sticks, interface_A or interface_B
color red, interface_A
color blue, interface_B

# 计算氢键
distance hbonds, chain A, chain B, 3.5, mode=2
```

## 对接质量验证

### 1. 与原始结构比较
```python
# 计算RMSD
import pymol
pymol.cmd.load('input/6666.pdb', 'original')
pymol.cmd.load('docked_result.pdb', 'docked')
pymol.cmd.align('docked', 'original')
```

### 2. 能量分析
- 使用FoldX或Rosetta计算结合能
- 分析界面残基贡献
- 检查几何合理性

### 3. 动力学验证
- 分子动力学模拟验证稳定性
- 分析结合模式的合理性

## 推荐工作流程

1. **快速测试**: 使用ClusPro进行初步对接
2. **精细对接**: 使用HADDOCK加入实验约束
3. **结果验证**: 与原始结构比较
4. **能量优化**: 使用Rosetta或CHARMM优化
5. **动力学验证**: MD模拟验证稳定性

## 注意事项

- 对接前确保蛋白质结构质量良好
- 考虑加入已知的相互作用约束
- 多种方法交叉验证结果
- 注意对接结果的生物学合理性

## 文件清单

- `chain_A.pdb` - 受体链
- `chain_B.pdb` - 配体链  
- `docking_analysis.pml` - PyMOL分析脚本
- `protein_docking.py` - 对接工具脚本
- `online_docking_guide.md` - 本指南文档
