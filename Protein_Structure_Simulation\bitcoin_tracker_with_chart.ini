[Rainmeter]
Update=1000
AccurateText=1
MiddleMouseUpAction=[!RefreshApp]

[Metadata]
Name=Bitcoin Tracker with Chart
Author=Enhanced
Version=5.0
Information=Bitcoin price tracker with 24h price chart

[Variables]
Seconds=60
GraphWidth=250
GraphHeight=80
MaxPoints=24

[MeasureParent]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true
RegExp=(?siU)"bitcoin":\{"usd":([0-9.]+),"usd_24h_change":([0-9.-]+)\}

[MeasurePrice]
Measure=WebParser
URL=[MeasureParent]
StringIndex=1

[MeasureChange]
Measure=WebParser
URL=[MeasureParent]
StringIndex=2

; Historical data for chart (24 hours, hourly intervals)
[MeasureHistory]
Measure=WebParser
UpdateRate=[#Seconds#*2]
URL=https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=1&interval=hourly
RegExp=(?siU)\[([0-9]+),([0-9.]+)\].*\[([0-9]+),([0-9.]+)\].*\[([0-9]+),([0-9.]+)\].*\[([0-9]+),([0-9.]+)\].*\[([0-9]+),([0-9.]+)\].*\[([0-9]+),([0-9.]+)\]

[MeasurePrice1]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=2

[MeasurePrice2]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=4

[MeasurePrice3]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=6

[MeasurePrice4]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=8

[MeasurePrice5]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=10

[MeasurePrice6]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=12

[MeasureGrowth]
Measure=Calc
Formula=[MeasureChange]
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption DownArrow Hidden 0][!SetOption UpArrow Hidden 1][!SetOption ChangeText FontColor 255,80,80,255]
IfCondition2=#CURRENTSECTION#>0
IfTrueAction2=[!SetOption UpArrow Hidden 0][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 80,255,80,255]
IfCondition3=#CURRENTSECTION#=0
IfTrueAction3=[!SetOption UpArrow Hidden 1][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 255,255,255,255]
DynamicVariables=1

[Background]
Meter=Shape
Shape=Rectangle 0,0,350,200,8 | Fill Color 15,15,25,220 | StrokeWidth 2 | Stroke Color 60,60,80,255

[HeaderBG]
Meter=Shape
Shape=Rectangle 5,5,340,75,5 | Fill Color 25,25,35,180 | StrokeWidth 1 | Stroke Color 80,80,100,255

[GraphBackground]
Meter=Shape
Shape=Rectangle 15,90,#GraphWidth#,#GraphHeight#,5 | Fill Color 10,10,15,200 | StrokeWidth 1 | Stroke Color 100,100,120,255

[GridLines]
Meter=Shape
Shape=Line 15,110,265,110 | Stroke Color 40,40,50,150
Shape2=Line 15,130,265,130 | Stroke Color 40,40,50,150
Shape3=Line 15,150,265,150 | Stroke Color 40,40,50,150
Shape4=Line 65,90,65,170 | Stroke Color 40,40,50,150
Shape5=Line 115,90,115,170 | Stroke Color 40,40,50,150
Shape6=Line 165,90,165,170 | Stroke Color 40,40,50,150
Shape7=Line 215,90,215,170 | Stroke Color 40,40,50,150

[UpArrow]
Meter=Shape
Shape=Line 20,55,40,40 | Stroke Color 80,255,80,255
Shape2=Line 40,40,37,38 | Stroke Color 80,255,80,255
Shape3=Line 40,40,38,42 | Stroke Color 80,255,80,255
Hidden=1

[DownArrow]
Meter=Shape
Shape=Line 20,40,40,55 | Stroke Color 255,80,80,255
Shape2=Line 40,55,37,57 | Stroke Color 255,80,80,255
Shape3=Line 40,55,38,53 | Stroke Color 255,80,80,255
Hidden=1

[PriceText]
Meter=String
MeasureName=MeasurePrice
NumOfDecimals=0
StringAlign=Center
X=175
Y=15
W=300
FontSize=26
FontColor=255,255,255,255
StringStyle=Bold
AntiAlias=1
Text=$%1

[CurrencyText]
Meter=String
Text=BTC/USD
StringAlign=Center
X=175
Y=45
W=300
FontSize=12
FontColor=180,180,200,255
AntiAlias=1

[ChangeText]
Meter=String
MeasureName=MeasureChange
NumOfDecimals=2
StringAlign=Left
X=50
Y=50
FontSize=12
FontColor=255,255,255,255
AntiAlias=1
Text=24h: %1%
DynamicVariables=1

[GraphTitle]
Meter=String
StringAlign=Left
X=20
Y=75
FontSize=10
FontColor=200,200,220,255
AntiAlias=1
Text=24 Hour Price Movement

[PriceChart]
Meter=Line
MeasureName=MeasurePrice
MeasureName2=MeasurePrice1
MeasureName3=MeasurePrice2
MeasureName4=MeasurePrice3
MeasureName5=MeasurePrice4
MeasureName6=MeasurePrice5
UpdateDivider=1
X=20
Y=95
W=#GraphWidth#
H=#GraphHeight#
PrimaryColor=100,150,255,220
PrimaryColor2=120,170,255,180
PrimaryColor3=140,190,255,140
PrimaryColor4=160,210,255,100
PrimaryColor5=180,230,255,80
PrimaryColor6=200,250,255,60
LineWidth=2
SolidColor=0,0,0,0
AntiAlias=1
AutoScale=1

[TimeLabels]
Meter=String
StringAlign=Left
X=20
Y=175
FontSize=7
FontColor=120,120,140,255
AntiAlias=1
Text=24h ago                    12h ago                    6h ago                     Now

[StatusText]
Meter=String
StringAlign=Left
X=20
Y=185
FontSize=8
FontColor=150,150,170,255
AntiAlias=1
Text=Updates every #Seconds# seconds • Data from CoinGecko

[DebugText]
Meter=String
MeasureName=MeasurePrice
StringAlign=Right
X=330
Y=185
FontSize=7
FontColor=100,100,120,255
AntiAlias=1
Text=Live: $%1
