[Rainmeter]
Update=1000
AccurateText=1
MiddleMouseUpAction=[!RefreshApp]

[Metadata]
Name=Bitcoin Tracker - Working Version
Author=Fixed
Version=3.0
Information=Working Bitcoin price tracker using CoinAPI

[Variables]
Seconds=30

[MeasureParent]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coinbase.com/v2/exchange-rates?currency=BTC
RegExp=(?siU)"USD":"([0-9.]+)"

[MeasurePrice]
Measure=WebParser
URL=[MeasureParent]
StringIndex=1

[MeasureParent2]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true
RegExp=(?siU)"usd":([0-9.]+).*"usd_24h_change":([0-9.-]+)

[MeasurePrice2]
Measure=WebParser
URL=[MeasureParent2]
StringIndex=1

[MeasureChange]
Measure=WebParser
URL=[MeasureParent2]
StringIndex=2

[MeasureGrowth]
Measure=Calc
Formula=[MeasureChange]
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption DownArrow Hidden 0][!SetOption UpArrow Hidden 1][!SetOption ChangeText FontColor 255,0,0,255]
IfCondition2=#CURRENTSECTION#>0
IfTrueAction2=[!SetOption UpArrow Hidden 0][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 0,255,0,255]
IfCondition3=#CURRENTSECTION#=0
IfTrueAction3=[!SetOption UpArrow Hidden 1][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 255,255,255,255]
DynamicVariables=1

[Background]
Meter=Shape
Shape=Rectangle 0,0,250,80,5 | Fill Color 0,0,0,150 | StrokeWidth 1 | Stroke Color 255,255,255,255

[UpArrow]
Meter=Shape
Shape=Line 10,45,30,30 | Stroke Color 0,255,0,255
Shape2=Line 30,30,27,28 | Stroke Color 0,255,0,255
Shape3=Line 30,30,28,32 | Stroke Color 0,255,0,255
Hidden=1

[DownArrow]
Meter=Shape
Shape=Line 10,30,30,45 | Stroke Color 255,0,0,255
Shape2=Line 30,45,27,47 | Stroke Color 255,0,0,255
Shape3=Line 30,45,28,43 | Stroke Color 255,0,0,255
Hidden=1

[PriceText]
Meter=String
MeasureName=MeasurePrice2
NumOfDecimals=0
StringAlign=Center
X=125
Y=15
W=200
FontSize=24
FontColor=255,255,255,255
StringStyle=Bold
AntiAlias=1
Text=$%1

[CurrencyText]
Meter=String
Text=BTC/USD
StringAlign=Center
X=125
Y=45
W=200
FontSize=12
FontColor=200,200,200,255
AntiAlias=1

[ChangeText]
Meter=String
MeasureName=MeasureChange
NumOfDecimals=2
StringAlign=Left
X=40
Y=35
FontSize=10
FontColor=255,255,255,255
AntiAlias=1
Text=24h: %1%
DynamicVariables=1

[StatusText]
Meter=String
StringAlign=Left
X=10
Y=60
FontSize=8
FontColor=150,150,150,255
AntiAlias=1
Text=Updated every #Seconds# seconds
