import tkinter as tk
from tkinter import messagebox
import random
import time

class Minesweeper:
    def __init__(self, rows=9, cols=9, mines=10):
        self.rows = rows
        self.cols = cols
        self.mines = mines
        self.game_over = False
        self.game_won = False
        self.start_time = None
        self.flags_count = 0
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("扫雷游戏")
        self.root.resizable(False, False)
        
        # 游戏状态
        self.board = [[0 for _ in range(cols)] for _ in range(rows)]
        self.revealed = [[False for _ in range(cols)] for _ in range(rows)]
        self.flagged = [[False for _ in range(cols)] for _ in range(rows)]
        self.buttons = [[None for _ in range(cols)] for _ in range(rows)]
        
        self.setup_ui()
        self.place_mines()
        self.calculate_numbers()
    
    def setup_ui(self):
        # 顶部信息栏
        info_frame = tk.Frame(self.root)
        info_frame.pack(pady=5)
        
        self.mines_label = tk.Label(info_frame, text=f"地雷: {self.mines}", font=("Arial", 12))
        self.mines_label.pack(side=tk.LEFT, padx=10)
        
        restart_btn = tk.Button(info_frame, text="重新开始", command=self.restart_game, 
                               font=("Arial", 10), bg="#4CAF50", fg="white")
        restart_btn.pack(side=tk.LEFT, padx=10)
        
        self.time_label = tk.Label(info_frame, text="时间: 0", font=("Arial", 12))
        self.time_label.pack(side=tk.LEFT, padx=10)
        
        # 游戏网格
        game_frame = tk.Frame(self.root)
        game_frame.pack(padx=10, pady=5)
        
        for i in range(self.rows):
            for j in range(self.cols):
                btn = tk.Button(game_frame, width=3, height=1, font=("Arial", 10, "bold"))
                btn.grid(row=i, column=j, padx=1, pady=1)
                btn.bind("<Button-1>", lambda e, r=i, c=j: self.left_click(r, c))
                btn.bind("<Button-3>", lambda e, r=i, c=j: self.right_click(r, c))
                self.buttons[i][j] = btn
        
        # 开始计时
        self.start_time = time.time()
        self.update_timer()
    
    def place_mines(self):
        """随机放置地雷"""
        mines_placed = 0
        while mines_placed < self.mines:
            row = random.randint(0, self.rows - 1)
            col = random.randint(0, self.cols - 1)
            if self.board[row][col] != -1:  # -1 表示地雷
                self.board[row][col] = -1
                mines_placed += 1
    
    def calculate_numbers(self):
        """计算每个格子周围的地雷数量"""
        for i in range(self.rows):
            for j in range(self.cols):
                if self.board[i][j] != -1:
                    count = 0
                    for di in [-1, 0, 1]:
                        for dj in [-1, 0, 1]:
                            if di == 0 and dj == 0:
                                continue
                            ni, nj = i + di, j + dj
                            if 0 <= ni < self.rows and 0 <= nj < self.cols:
                                if self.board[ni][nj] == -1:
                                    count += 1
                    self.board[i][j] = count
    
    def left_click(self, row, col):
        """左键点击处理"""
        if self.game_over or self.game_won or self.flagged[row][col]:
            return
        
        if self.board[row][col] == -1:
            # 点到地雷，游戏结束
            self.game_over = True
            self.reveal_all_mines()
            messagebox.showinfo("游戏结束", "你踩到地雷了！游戏结束。")
        else:
            self.reveal_cell(row, col)
            self.check_win()
    
    def right_click(self, row, col):
        """右键点击处理（标记/取消标记）"""
        if self.game_over or self.game_won or self.revealed[row][col]:
            return
        
        if self.flagged[row][col]:
            # 取消标记
            self.flagged[row][col] = False
            self.buttons[row][col].config(text="", bg="SystemButtonFace")
            self.flags_count -= 1
        else:
            # 添加标记
            self.flagged[row][col] = True
            self.buttons[row][col].config(text="🚩", bg="yellow")
            self.flags_count += 1
        
        self.mines_label.config(text=f"地雷: {self.mines - self.flags_count}")
    
    def reveal_cell(self, row, col):
        """揭开格子"""
        if self.revealed[row][col] or self.flagged[row][col]:
            return
        
        self.revealed[row][col] = True
        value = self.board[row][col]
        
        if value == 0:
            self.buttons[row][col].config(text="", bg="lightgray", relief="sunken")
            # 自动揭开周围的空格子
            for di in [-1, 0, 1]:
                for dj in [-1, 0, 1]:
                    ni, nj = row + di, col + dj
                    if 0 <= ni < self.rows and 0 <= nj < self.cols:
                        self.reveal_cell(ni, nj)
        else:
            colors = ["", "blue", "green", "red", "purple", "maroon", "turquoise", "black", "gray"]
            self.buttons[row][col].config(text=str(value), bg="lightgray", 
                                        fg=colors[value], relief="sunken")
    
    def reveal_all_mines(self):
        """揭开所有地雷"""
        for i in range(self.rows):
            for j in range(self.cols):
                if self.board[i][j] == -1:
                    self.buttons[i][j].config(text="💣", bg="red")
    
    def check_win(self):
        """检查是否获胜"""
        for i in range(self.rows):
            for j in range(self.cols):
                if self.board[i][j] != -1 and not self.revealed[i][j]:
                    return
        
        self.game_won = True
        messagebox.showinfo("恭喜", "你赢了！所有地雷都被找到了！")
    
    def update_timer(self):
        """更新计时器"""
        if not self.game_over and not self.game_won and self.start_time:
            elapsed = int(time.time() - self.start_time)
            self.time_label.config(text=f"时间: {elapsed}")
        
        self.root.after(1000, self.update_timer)
    
    def restart_game(self):
        """重新开始游戏"""
        self.game_over = False
        self.game_won = False
        self.flags_count = 0
        self.start_time = time.time()
        
        # 重置游戏状态
        self.board = [[0 for _ in range(self.cols)] for _ in range(self.rows)]
        self.revealed = [[False for _ in range(self.cols)] for _ in range(self.rows)]
        self.flagged = [[False for _ in range(self.cols)] for _ in range(self.rows)]
        
        # 重置按钮
        for i in range(self.rows):
            for j in range(self.cols):
                self.buttons[i][j].config(text="", bg="SystemButtonFace", relief="raised")
        
        # 重新放置地雷和计算数字
        self.place_mines()
        self.calculate_numbers()
        
        # 重置标签
        self.mines_label.config(text=f"地雷: {self.mines}")
    
    def run(self):
        """运行游戏"""
        self.root.mainloop()

if __name__ == "__main__":
    # 创建并运行扫雷游戏
    game = Minesweeper(rows=9, cols=9, mines=10)  # 初级难度：9x9网格，10个地雷
    game.run()
