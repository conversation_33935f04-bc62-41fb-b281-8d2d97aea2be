[Rainmeter]
Update=1000
AccurateText=1
MiddleMouseUpAction=[!RefreshApp]

[Metadata]
Name=Bitcoin Tracker - Final Version
Author=Fixed
Version=4.0
Information=Working Bitcoin price tracker

[Variables]
Seconds=30
GraphWidth=200
GraphHeight=60

[MeasureParent]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true
RegExp=(?siU)"bitcoin":\{"usd":([0-9.]+),"usd_24h_change":([0-9.-]+)\}

[MeasureHistory]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=1&interval=hourly
RegExp=(?siU)"prices":\[(.+?)\]

[MeasureHistoryData]
Measure=WebParser
URL=[MeasureHistory]
StringIndex=1

[MeasurePrice]
Measure=WebParser
URL=[MeasureParent]
StringIndex=1

[MeasureChange]
Measure=WebParser
URL=[MeasureParent]
StringIndex=2

[MeasureGrowth]
Measure=Calc
Formula=[MeasureChange]
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption DownArrow Hidden 0][!SetOption UpArrow Hidden 1][!SetOption ChangeText FontColor 255,0,0,255]
IfCondition2=#CURRENTSECTION#>0
IfTrueAction2=[!SetOption UpArrow Hidden 0][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 0,255,0,255]
IfCondition3=#CURRENTSECTION#=0
IfTrueAction3=[!SetOption UpArrow Hidden 1][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 255,255,255,255]
DynamicVariables=1

[Background]
Meter=Shape
Shape=Rectangle 0,0,320,180,5 | Fill Color 0,0,0,150 | StrokeWidth 1 | Stroke Color 255,255,255,255

[GraphBackground]
Meter=Shape
Shape=Rectangle 10,110,#GraphWidth#,#GraphHeight#,3 | Fill Color 20,20,20,200 | StrokeWidth 1 | Stroke Color 100,100,100,255

[UpArrow]
Meter=Shape
Shape=Line 15,65,35,50 | Stroke Color 0,255,0,255
Shape2=Line 35,50,32,48 | Stroke Color 0,255,0,255
Shape3=Line 35,50,33,52 | Stroke Color 0,255,0,255
Hidden=1

[DownArrow]
Meter=Shape
Shape=Line 15,50,35,65 | Stroke Color 255,0,0,255
Shape2=Line 35,65,32,67 | Stroke Color 255,0,0,255
Shape3=Line 35,65,33,63 | Stroke Color 255,0,0,255
Hidden=1

[PriceText]
Meter=String
MeasureName=MeasurePrice
NumOfDecimals=0
StringAlign=Center
X=160
Y=15
W=300
FontSize=24
FontColor=255,255,255,255
StringStyle=Bold
AntiAlias=1
Text=$%1

[CurrencyText]
Meter=String
Text=BTC/USD
StringAlign=Center
X=160
Y=40
W=300
FontSize=11
FontColor=200,200,200,255
AntiAlias=1

[ChangeText]
Meter=String
MeasureName=MeasureChange
NumOfDecimals=2
StringAlign=Left
X=45
Y=55
FontSize=11
FontColor=255,255,255,255
AntiAlias=1
Text=24h: %1%
DynamicVariables=1

[GraphTitle]
Meter=String
StringAlign=Left
X=15
Y=90
FontSize=9
FontColor=200,200,200,255
AntiAlias=1
Text=24h Price Chart (4h intervals)

[PriceGraph]
Meter=Line
MeasureName=MeasurePrice
UpdateDivider=1
X=15
Y=115
W=#GraphWidth#
H=#GraphHeight#
PrimaryColor=100,150,255,200
SolidColor=0,0,0,0
AntiAlias=1
AutoScale=1

[StatusText]
Meter=String
StringAlign=Left
X=15
Y=155
FontSize=7
FontColor=150,150,150,255
AntiAlias=1
Text=Updated every #Seconds# seconds

[DebugText]
Meter=String
MeasureName=MeasurePrice
StringAlign=Left
X=220
Y=155
FontSize=6
FontColor=100,100,100,255
AntiAlias=1
Text=Current: $%1
