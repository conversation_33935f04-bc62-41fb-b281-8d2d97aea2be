# Beautiful PyMOL script for protein chain interactions
# Usage: @beautiful_interactions.pml

# Load structure
load output_6666_interactions.pdb

# Basic setup
hide everything
bg_color white
set depth_cue, 0
set ray_trace_mode, 1

# Show cartoon for all residues with transparency
show cartoon, all
set cartoon_transparency, 0.3

# Beautiful color scheme for chains
color slate, chain A
color wheat, chain B

# Define interaction residues
select interaction_residues, (chain A and resi 23+24+26+42+44+45+64+66+68+88+92+93+112+114+116+117+138+140+161+163+185+215+216+217+218) or (chain B and resi 10+74+76+77+78+79+80+152+199+200+201+229+230+231+232+233+234+235+236+237+238)

# Show sticks for interaction residues only
show sticks, interaction_residues
set stick_radius, 0.2

# Beautiful colors for interaction residues
color deepblue, chain A and interaction_residues
color deeppurple, chain B and interaction_residues

# Show CA atoms as spheres for interaction residues
show spheres, interaction_residues and name CA
set sphere_scale, 0.25
color tv_blue, chain A and interaction_residues and name CA
color tv_red, chain B and interaction_residues and name CA

# Highlight hotspots (residues with >40 interactions)
select hotspots, (chain A and resi 44+215) or (chain B and resi 77+232)
color brightorange, hotspots
set sphere_scale, 0.4, hotspots and name CA

# Show hydrogen bonds between chains
distance hbonds, chain A, chain B, 3.5, mode=2
hide labels, hbonds
color yellow, hbonds
set dash_width, 3
set dash_gap, 0.2

# Add labels for key residues
label chain A and resi 44 and name CA, "A44-TYR"
label chain A and resi 215 and name CA, "A215-TRP"
label chain B and resi 77 and name CA, "B77-ASP"
label chain B and resi 232 and name CA, "B232-HIS"

# Label settings
set label_size, 16
set label_color, black
set label_outline_color, white

# Create a nice view
orient
zoom
set stick_quality, 15
set sphere_quality, 2

# Create scenes for different views
scene scene001, store, "Overall View"

# Interaction-only view
hide cartoon
show sticks, interaction_residues
show spheres, interaction_residues and name CA
scene scene002, store, "Interactions Only"

# Surface view
scene scene001, recall
show surface, chain A
show surface, chain B
set surface_color, lightblue, chain A
set surface_color, lightpink, chain B
set transparency, 0.6
scene scene003, store, "Surface View"

# Return to overall view
scene scene001, recall

# Final touches
set antialias, 2
set cartoon_fancy_helices, 1
set cartoon_smooth_loops, 1

print "Beautiful interaction visualization loaded!"
print "Scenes: scene001 (overall), scene002 (interactions only), scene003 (surface)"
print "Only inter-chain interacting residues are shown as sticks"
