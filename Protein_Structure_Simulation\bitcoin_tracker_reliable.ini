[Rainmeter]
Update=1000
AccurateText=1
MiddleMouseUpAction=[!RefreshApp]

[Metadata]
Name=Bitcoin Tracker - Reliable Version
Author=Reliable
Version=6.0
Information=Reliable Bitcoin price tracker with simple chart

[Variables]
Seconds=60
GraphWidth=220
GraphHeight=60

[MeasureParent]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true
RegExp=(?siU)"bitcoin":\{"usd":([0-9.]+),"usd_24h_change":([0-9.-]+)\}

[MeasurePrice]
Measure=WebParser
URL=[MeasureParent]
StringIndex=1

[MeasureChange]
Measure=WebParser
URL=[MeasureParent]
StringIndex=2

; Simple price history using calc measures to store previous values
[MeasurePriceHistory]
Measure=Calc
Formula=[MeasurePrice]
AverageSize=20

[MeasureGrowth]
Measure=Calc
Formula=[MeasureChange]
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption DownArrow Hidden 0][!SetOption UpArrow Hidden 1][!SetOption ChangeText FontColor 255,100,100,255]
IfCondition2=#CURRENTSECTION#>0
IfTrueAction2=[!SetOption UpArrow Hidden 0][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 100,255,100,255]
IfCondition3=#CURRENTSECTION#=0
IfTrueAction3=[!SetOption UpArrow Hidden 1][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 255,255,255,255]
DynamicVariables=1

[Background]
Meter=Shape
Shape=Rectangle 0,0,300,160,8 | Fill Color 20,20,30,240 | StrokeWidth 2 | Stroke Color 80,80,100,255

[HeaderBG]
Meter=Shape
Shape=Rectangle 8,8,284,70,5 | Fill Color 30,30,40,200 | StrokeWidth 1 | Stroke Color 100,100,120,255

[GraphBackground]
Meter=Shape
Shape=Rectangle 15,85,#GraphWidth#,#GraphHeight#,4 | Fill Color 15,15,20,220 | StrokeWidth 1 | Stroke Color 120,120,140,255

[UpArrow]
Meter=Shape
Shape=Line 20,50,35,38 | Stroke Color 100,255,100,255
Shape2=Line 35,38,33,36 | Stroke Color 100,255,100,255
Shape3=Line 35,38,34,40 | Stroke Color 100,255,100,255
Hidden=1

[DownArrow]
Meter=Shape
Shape=Line 20,38,35,50 | Stroke Color 255,100,100,255
Shape2=Line 35,50,33,52 | Stroke Color 255,100,100,255
Shape3=Line 35,50,34,48 | Stroke Color 255,100,100,255
Hidden=1

[PriceText]
Meter=String
MeasureName=MeasurePrice
NumOfDecimals=0
StringAlign=Center
X=150
Y=18
W=280
FontSize=22
FontColor=255,255,255,255
StringStyle=Bold
AntiAlias=1
Text=$%1

[CurrencyText]
Meter=String
Text=BTC/USD
StringAlign=Center
X=150
Y=42
W=280
FontSize=10
FontColor=180,180,200,255
AntiAlias=1

[ChangeText]
Meter=String
MeasureName=MeasureChange
NumOfDecimals=2
StringAlign=Left
X=45
Y=45
FontSize=10
FontColor=255,255,255,255
AntiAlias=1
Text=24h: %1%
DynamicVariables=1

[GraphTitle]
Meter=String
StringAlign=Left
X=20
Y=70
FontSize=8
FontColor=200,200,220,255
AntiAlias=1
Text=Price Trend (Live Updates)

[PriceChart]
Meter=Line
MeasureName=MeasurePriceHistory
UpdateDivider=1
X=20
Y=90
W=#GraphWidth#
H=#GraphHeight#
PrimaryColor=120,160,255,200
LineWidth=2
SolidColor=0,0,0,0
AntiAlias=1
AutoScale=1

[StatusText]
Meter=String
StringAlign=Left
X=20
Y=150
FontSize=7
FontColor=150,150,170,255
AntiAlias=1
Text=Updates every #Seconds#s • CoinGecko API

[DebugPrice]
Meter=String
MeasureName=MeasurePrice
StringAlign=Right
X=280
Y=150
FontSize=6
FontColor=120,120,140,255
AntiAlias=1
Text=Live: $%1

[DebugChange]
Meter=String
MeasureName=MeasureChange
StringAlign=Center
X=150
Y=58
FontSize=7
FontColor=120,120,140,255
AntiAlias=1
Text=Change: %1%
