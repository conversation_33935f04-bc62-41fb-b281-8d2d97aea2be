#!/usr/bin/env python3
"""
蛋白质对接脚本
从复合物PDB文件中分离两条链，然后进行对接
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

class ProteinDocker:
    """蛋白质对接类"""
    
    def __init__(self, pdb_file):
        self.pdb_file = pdb_file
        self.chain_a_file = "chain_A.pdb"
        self.chain_b_file = "chain_B.pdb"
        self.docked_file = "docked_complex.pdb"
        
    def separate_chains(self):
        """分离PDB文件中的两条链"""
        print("正在分离蛋白质链...")
        
        chain_a_atoms = []
        chain_b_atoms = []
        header_lines = []
        
        with open(self.pdb_file, 'r') as f:
            for line in f:
                if line.startswith('HEADER') or line.startswith('TITLE') or \
                   line.startswith('COMPND') or line.startswith('SOURCE') or \
                   line.startswith('REMARK'):
                    header_lines.append(line)
                elif line.startswith('ATOM'):
                    chain_id = line[21]
                    if chain_id == 'A':
                        chain_a_atoms.append(line)
                    elif chain_id == 'B':
                        chain_b_atoms.append(line)
        
        # 写入链A文件
        with open(self.chain_a_file, 'w') as f:
            for line in header_lines:
                f.write(line)
            f.write("REMARK   1 CHAIN A EXTRACTED FOR DOCKING\n")
            for line in chain_a_atoms:
                f.write(line)
            f.write("END\n")
        
        # 写入链B文件
        with open(self.chain_b_file, 'w') as f:
            for line in header_lines:
                f.write(line)
            f.write("REMARK   1 CHAIN B EXTRACTED FOR DOCKING\n")
            for line in chain_b_atoms:
                f.write(line)
            f.write("END\n")
        
        print(f"链A已保存到: {self.chain_a_file} ({len(chain_a_atoms)} 个原子)")
        print(f"链B已保存到: {self.chain_b_file} ({len(chain_b_atoms)} 个原子)")
        
        return self.chain_a_file, self.chain_b_file
    
    def dock_with_pymol(self):
        """使用PyMOL进行简单的对接"""
        print("使用PyMOL进行对接...")
        
        pymol_script = f"""
# PyMOL对接脚本
load {self.chain_a_file}, receptor
load {self.chain_b_file}, ligand

# 将链B移动到远离链A的位置
translate [50, 0, 0], ligand

# 基本对接：将链B移回到与链A接触的位置
# 这是一个简化的对接过程
orient receptor
center receptor

# 尝试不同的对接姿态
for i in range(10):
    # 随机旋转和平移链B
    rotate x, 36*i, ligand
    rotate y, 25*i, ligand
    rotate z, 15*i, ligand
    
    # 检查是否有合理的接触
    distance contacts, receptor, ligand, 4.0
    
    # 保存当前姿态
    save docked_pose_{{i}}.pdb, receptor or ligand

print "对接完成，生成了10个可能的对接姿态"
quit
"""
        
        with open("dock_script.pml", 'w') as f:
            f.write(pymol_script)
        
        print("PyMOL对接脚本已生成: dock_script.pml")
        print("请在PyMOL中运行: @dock_script.pml")
        
    def dock_with_hdock(self):
        """使用HDock进行对接（如果可用）"""
        print("准备使用HDock进行对接...")
        
        # 检查HDock是否可用
        try:
            result = subprocess.run(['hdock'], capture_output=True, text=True)
            hdock_available = True
        except FileNotFoundError:
            hdock_available = False
        
        if not hdock_available:
            print("HDock未安装或不在PATH中")
            print("您可以从 http://hdock.phys.hust.edu.cn/ 下载HDock")
            return False
        
        # 运行HDock
        cmd = f"hdock {self.chain_a_file} {self.chain_b_file} -out docked"
        print(f"运行命令: {cmd}")
        
        try:
            result = subprocess.run(cmd.split(), capture_output=True, text=True)
            if result.returncode == 0:
                print("HDock对接成功完成")
                print("结果文件: docked.out")
                return True
            else:
                print(f"HDock运行失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"运行HDock时出错: {e}")
            return False
    
    def create_docking_pymol_script(self):
        """创建用于对接分析的PyMOL脚本"""
        script_content = f"""
# 蛋白质对接分析脚本

# 加载分离的链
load {self.chain_a_file}, chainA
load {self.chain_b_file}, chainB

# 基本显示设置
hide everything
show cartoon
color marine, chainA
color salmon, chainB

# 将链B移动到远离链A的位置以便观察
translate [30, 0, 0], chainB

# 创建对接分析函数
python
def dock_analysis():
    # 简单的刚体对接模拟
    import random
    
    best_score = float('inf')
    best_pose = 0
    
    for i in range(20):
        # 随机旋转和平移
        rx = random.uniform(0, 360)
        ry = random.uniform(0, 360) 
        rz = random.uniform(0, 360)
        tx = random.uniform(-10, 10)
        ty = random.uniform(-10, 10)
        tz = random.uniform(-10, 10)
        
        # 应用变换
        cmd.rotate('x', rx, 'chainB')
        cmd.rotate('y', ry, 'chainB')
        cmd.rotate('z', rz, 'chainB')
        cmd.translate([tx, ty, tz], 'chainB')
        
        # 计算接触数（简化的评分函数）
        cmd.distance('contacts', 'chainA', 'chainB', 4.0)
        contacts = cmd.count_atoms('contacts')
        
        if contacts > 0:
            score = 1.0 / contacts  # 简化评分
            if score < best_score:
                best_score = score
                best_pose = i
                cmd.save(f'best_dock_pose_{{i}}.pdb', 'chainA or chainB')
        
        # 重置位置
        cmd.load('{self.chain_b_file}', 'chainB')
        cmd.translate([30, 0, 0], 'chainB')
        cmd.delete('contacts')
    
    print(f"最佳对接姿态: {{best_pose}}, 评分: {{best_score}}")

python end

# 运行对接分析
dock_analysis

# 显示最终结果
orient
zoom
"""
        
        with open("docking_analysis.pml", 'w') as f:
            f.write(script_content)
        
        print("对接分析脚本已生成: docking_analysis.pml")
        return "docking_analysis.pml"
    
    def run_simple_docking(self):
        """运行简单的对接流程"""
        print("开始蛋白质对接流程...")
        
        # 1. 分离链
        chain_a, chain_b = self.separate_chains()
        
        # 2. 创建PyMOL对接脚本
        script_file = self.create_docking_pymol_script()
        
        # 3. 尝试使用HDock（如果可用）
        hdock_success = self.dock_with_hdock()
        
        print("\n对接流程完成！")
        print("生成的文件:")
        print(f"- {chain_a} (受体链A)")
        print(f"- {chain_b} (配体链B)")
        print(f"- {script_file} (PyMOL对接分析脚本)")
        
        if hdock_success:
            print("- docked.out (HDock对接结果)")
        
        print("\n使用方法:")
        print(f"1. 在PyMOL中运行: @{script_file}")
        print("2. 或者使用专业对接软件如HADDOCK、ClusPro等")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='蛋白质对接工具')
    parser.add_argument('pdb_file', help='输入PDB文件路径')
    parser.add_argument('--method', choices=['pymol', 'hdock', 'simple'], 
                       default='simple', help='对接方法')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.pdb_file):
        print(f"错误: 文件 {args.pdb_file} 不存在")
        return 1
    
    docker = ProteinDocker(args.pdb_file)
    
    if args.method == 'simple':
        docker.run_simple_docking()
    elif args.method == 'pymol':
        docker.separate_chains()
        docker.dock_with_pymol()
    elif args.method == 'hdock':
        docker.separate_chains()
        docker.dock_with_hdock()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
