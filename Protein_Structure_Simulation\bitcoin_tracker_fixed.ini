[Rainmeter]
Update=1000
AccurateText=1
MiddleMouseUpAction=[!RefreshApp]

[Metadata]
Name=Simple Bitcoin Tracker
Author=Deofol
Version=1.0
License=GPLv3
Information=Shows ongoing rate for the USD/BTC pair, max and min 24 hour values and a reference graph through time running the skin.

[Variables]
Seconds=60
; Set how many seconds should each update take

[MeasureParent]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://www.bitstamp.net/api/v2/ticker/btcusd/
RegExp=(?siU)"high":"([^"]*)".*"last":"([^"]*)".*"bid":"([^"]*)".*"low":"([^"]*)"

[MeasureDebug]
Measure=WebParser
URL=[MeasureParent]
StringIndex=0

[MeasurePrice]
Measure=WebParser
URL=[MeasureParent]
StringIndex=3

[MeasureLast]
Measure=WebParser
URL=[MeasureParent]
StringIndex=2

[MeasureLow]
Measure=WebParser
URL=[MeasureParent]
StringIndex=4

[MeasureHigh]
Measure=WebParser
URL=[MeasureParent]
StringIndex=1

[MeasureLine]
Measure=Calc
Formula=(100*([MeasurePrice:2]-[MeasureLow:2]))/([MeasureHigh:2]-[MeasureLow:2])
MinValue=0
MaxValue=100
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption MeterLine PrimaryColor 255,0,0,100]
IfCondition2=(#CURRENTSECTION#>=0) && (#CURRENTSECTION#<=100)
IfTrueAction2=[!SetOption MeterLine PrimaryColor 255,255,255,100]
IfCondition3=#CURRENTSECTION#>100
IfTrueAction3=[!SetOption MeterLine PrimaryColor 0,255,0,100]
DynamicVariables=1

[MeasureGrowth]
Measure=Calc
Formula=[MeasurePrice:2]-[MeasureLast:2]
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption DownArrow Hidden 0][!SetOption UpArrow Hidden 1]
IfCondition2=#CURRENTSECTION#>0
IfTrueAction2=[!SetOption UpArrow Hidden 0][!SetOption DownArrow Hidden 1]
DynamicVariables=1

[GraphBG]
Meter=Shape
Shape=Rectangle 0,0,160,45,3 | Fill Color 0,0,0,100 | StrokeWidth 1 | Stroke Color 255,255,255,255

[UpArrow]
Meter=Shape
Shape=Line 4,27,40,18 | Stroke Color 0,255,0,255
Shape2=Line 40,18,35,16 | Stroke Color 0,255,0,255
Shape3=Line 40,18,37,21 | Stroke Color 0,255,0,255
Hidden=1

[DownArrow]
Meter=Shape
Shape=Line 4,18,40,27 | Stroke Color 255,0,0,255
Shape2=Line 40,27,35,29 | Stroke Color 255,0,0,255
Shape3=Line 40,27,37,24 | Stroke Color 255,0,0,255
Hidden=1

[MeterLine]
Meter=Line
MeasureName=MeasureLine
UpdateDivider=#Seconds#
X=3
Y=3
W=154
H=39
PrimaryColor=255,255,255,100
SolidColor=0,0,0,0
AntiAlias=1

[MeterPrice]
Meter=String
MeasureName=MeasurePrice
W=110
H=70
X=100
Y=-3
FontSize=20
FontColor=255,255,255,255
InlineSetting=Face | Roboto-Bold
StringAlign=Center
StringStyle=Bold
AntiAlias=1

[CurrencyText]
Meter=String
Text=USD - BTC
StringAlign=Center
StringStyle=Bold
X=r
Y=27r
FontSize=10
FontColor=255,255,255,255
InlineSetting=Face | Roboto-Bold
AntiAlias=1

[HighText]
Meter=String
MeasureName=MeasureHigh
StringAlign=Left
X=3
Y=4
FontSize=8
FontColor=0,255,0,255
InlineSetting=Face | Roboto-Bold
AntiAlias=1

[LowText]
Meter=String
MeasureName=MeasureLow
StringAlign=Left
X=3
Y=27
FontSize=8
FontColor=255,0,0,255
InlineSetting=Face | Roboto-Bold
AntiAlias=1

[DebugText]
Meter=String
MeasureName=MeasureDebug
StringAlign=Left
X=5
Y=50
W=200
H=100
FontSize=6
FontColor=255,255,0,255
ClipString=1
AntiAlias=1
