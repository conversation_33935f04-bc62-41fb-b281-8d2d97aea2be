[Rainmeter]
Update=1000
AccurateText=1
MiddleMouseUpAction=[!RefreshApp]

[Metadata]
Name=Simple Bitcoin Tracker - Fixed
Author=Deofol (Modified)
Version=2.0
License=GPLv3
Information=Shows ongoing rate for the USD/BTC pair using CoinGecko API

[Variables]
Seconds=30
; Set how many seconds should each update take

[MeasureParent]
Measure=WebParser
UpdateRate=#Seconds#
URL=https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_high_low=true&include_24hr_change=true
RegExp=(?siU)"usd":([0-9.]+).*"usd_24h_high":([0-9.]+).*"usd_24h_low":([0-9.]+).*"usd_24h_change":([0-9.-]+)

[MeasurePrice]
Measure=WebParser
URL=[MeasureParent]
StringIndex=1

[MeasureHigh]
Measure=WebParser
URL=[MeasureParent]
StringIndex=2

[MeasureLow]
Measure=WebParser
URL=[MeasureParent]
StringIndex=3

[MeasureChange]
Measure=WebParser
URL=[MeasureParent]
StringIndex=4

[MeasureLine]
Measure=Calc
Formula=(100*([MeasurePrice]-[MeasureLow]))/([MeasureHigh]-[MeasureLow])
MinValue=0
MaxValue=100
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption MeterLine PrimaryColor 255,0,0,100]
IfCondition2=(#CURRENTSECTION#>=0) && (#CURRENTSECTION#<=100)
IfTrueAction2=[!SetOption MeterLine PrimaryColor 255,255,255,100]
IfCondition3=#CURRENTSECTION#>100
IfTrueAction3=[!SetOption MeterLine PrimaryColor 0,255,0,100]
DynamicVariables=1

[MeasureGrowth]
Measure=Calc
Formula=[MeasureChange]
IfCondition=#CURRENTSECTION#<0
IfTrueAction=[!SetOption DownArrow Hidden 0][!SetOption UpArrow Hidden 1][!SetOption ChangeText FontColor 255,0,0,255]
IfCondition2=#CURRENTSECTION#>0
IfTrueAction2=[!SetOption UpArrow Hidden 0][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 0,255,0,255]
IfCondition3=#CURRENTSECTION#=0
IfTrueAction3=[!SetOption UpArrow Hidden 1][!SetOption DownArrow Hidden 1][!SetOption ChangeText FontColor 255,255,255,255]
DynamicVariables=1

[GraphBG]
Meter=Shape
Shape=Rectangle 0,0,200,60,3 | Fill Color 0,0,0,100 | StrokeWidth 1 | Stroke Color 255,255,255,255

[UpArrow]
Meter=Shape
Shape=Line 4,35,25,25 | Stroke Color 0,255,0,255
Shape2=Line 25,25,22,23 | Stroke Color 0,255,0,255
Shape3=Line 25,25,23,27 | Stroke Color 0,255,0,255
Hidden=1

[DownArrow]
Meter=Shape
Shape=Line 4,25,25,35 | Stroke Color 255,0,0,255
Shape2=Line 25,35,22,37 | Stroke Color 255,0,0,255
Shape3=Line 25,35,23,33 | Stroke Color 255,0,0,255
Hidden=1

[MeterLine]
Meter=Line
MeasureName=MeasureLine
UpdateDivider=#Seconds#
X=3
Y=3
W=194
H=54
PrimaryColor=255,255,255,100
SolidColor=0,0,0,0
AntiAlias=1

[MeterPrice]
Meter=String
MeasureName=MeasurePrice
NumOfDecimals=0
W=150
H=30
X=120
Y=5
FontSize=18
FontColor=255,255,255,255
StringAlign=Center
StringStyle=Bold
AntiAlias=1
Text=$%1

[CurrencyText]
Meter=String
Text=BTC/USD
StringAlign=Center
StringStyle=Bold
X=120
Y=25
W=150
FontSize=10
FontColor=255,255,255,255
AntiAlias=1

[HighText]
Meter=String
MeasureName=MeasureHigh
NumOfDecimals=0
StringAlign=Left
X=5
Y=8
FontSize=8
FontColor=0,255,0,255
AntiAlias=1
Text=H: $%1

[LowText]
Meter=String
MeasureName=MeasureLow
NumOfDecimals=0
StringAlign=Left
X=5
Y=18
FontSize=8
FontColor=255,0,0,255
AntiAlias=1
Text=L: $%1

[ChangeText]
Meter=String
MeasureName=MeasureChange
NumOfDecimals=2
StringAlign=Left
X=5
Y=28
FontSize=8
FontColor=255,255,255,255
AntiAlias=1
Text=24h: %1%
DynamicVariables=1
